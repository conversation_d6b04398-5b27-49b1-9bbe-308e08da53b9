"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import DatePicker, { registerLocale } from "react-datepicker";
import { ru } from "date-fns/locale";
import "react-datepicker/dist/react-datepicker.css";
import API_CONFIG from "../../../config/api";
import { useAuth } from "../../../context/AuthContext";
import authService from "../../../services/auth.service";

// Регистрируем русскую локализацию
registerLocale("ru", ru);

const TenderProposalContainer = styled.div`
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderProposalContainer.displayName = "TenderProposalContainer";

const BackButtonMiniSection = styled.div`
  height: 70px;
  padding: 24px 160px 12px 160px;
`;
BackButtonMiniSection.displayName = "BackButtonMiniSection";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const BackButton = styled.button`
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;
  border: none;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 900;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const TenderInfoCard = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
`;
TenderInfoCard.displayName = "TenderInfoCard";

const TenderTitle = styled.h3`
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin-bottom: 12px;
`;
TenderTitle.displayName = "TenderTitle";

const TenderDetail = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;

  strong {
    color: #333;
  }
`;
TenderDetail.displayName = "TenderDetail";

const ProductFormCard = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;
ProductFormCard.displayName = "ProductFormCard";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 24px;
  font-weight: 400;
  color: #434a54;
  line-height: 32px;
  margin-bottom: 10px;
`;
ProductTitle.displayName = "ProductTitle";

const Label = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #969ea7;
  margin-bottom: 10px;
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  padding-right: 40px;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
Input.displayName = "Input";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 200px;
  position: relative;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 75px;
  width: 100%;
  resize: vertical;
  color: #434a54;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TextArea.displayName = "TextArea";

const ActionContainer = styled.div`
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
ActionContainer.displayName = "ActionContainer";

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

const SubmitButton = styled.button`
  background-color: #28a745;
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 32px auto 0;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #218838;
  }
`;
SubmitButton.displayName = "SubmitButton";

const NoTenderMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;
NoTenderMessage.displayName = "NoTenderMessage";

const DateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: white;
  border: 2px solid #0066cc;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  color: #0066cc;
  font-weight: 600;
  margin-top: 0px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f8ff;
  }
`;
DateButton.displayName = "DateButton";

const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #434a54;
  margin-bottom: 10px;
  margin-top: 10px;

  &:hover {
    background-color: #f8f9fa;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span`
  font-size: 14px;
  color: #434a54;
`;
UploadText.displayName = "UploadText";

const HiddenFileInput = styled.input`
  display: none;
`;
HiddenFileInput.displayName = "HiddenFileInput";

const AttachedFilesList = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;
AttachedFilesList.displayName = "AttachedFilesList";

const AttachedFileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
`;
AttachedFileItem.displayName = "AttachedFileItem";

const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;
FileInfo.displayName = "FileInfo";

const FileName = styled.span`
  font-size: 14px;
  color: #333;
  font-weight: 500;
`;
FileName.displayName = "FileName";

const FileSize = styled.span`
  font-size: 12px;
  color: #666;
`;
FileSize.displayName = "FileSize";

const RemoveFileButton = styled.button`
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5c6cb;
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;
ModalOverlay.displayName = "ModalOverlay";

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 32px;
  max-width: 610px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
`;
ModalContent.displayName = "ModalContent";

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;
ModalHeader.displayName = "ModalHeader";

const ModalTitle = styled.h3`
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin: 0;
`;
ModalTitle.displayName = "ModalTitle";

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;

  &:hover {
    color: #333;
  }
`;
CloseButton.displayName = "CloseButton";

const CalendarContainer = styled.div`
  .react-datepicker {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-family: inherit;
    background: white;
    padding: 32px;
  }

  .react-datepicker__header {
    background: white;
    border-bottom: none;
    padding: 0 0 20px 0;
  }

  .react-datepicker__current-month {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }

  .react-datepicker__navigation {
    top: 32px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      color: #e9ecef;
    }

    &--previous {
      left: 20px;
    }

    &--next {
      right: 20px;
    }
  }

  .react-datepicker__navigation-icon {
    &::before {
      border-color: #666;
      border-width: 2px 2px 0 0;
      width: 8px;
      height: 8px;
    }
  }

  .react-datepicker__day-names {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 10px;
  }

  .react-datepicker__day-name {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin: 0;
  }

  .react-datepicker__month {
  }

  .react-datepicker__week {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 0;
  }

  .react-datepicker__day {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 400;
    color: #333;
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      background: #f0f8ff;
      color: #0066cc;
    }

    &--selected {
      background: #0066cc !important;
      color: white !important;
      font-weight: 600;
    }

    &--today {
      background: #e3f2fd;
      color: #0066cc;
      font-weight: 600;
    }

    &--weekend {
      color: #dc3545;
    }

    &--outside-month {
      color: #ccc;
    }

    &--disabled {
      color: #ccc;
      cursor: not-allowed;

      &:hover {
        background: transparent;
        color: #ccc;
      }
    }
  }

  .react-datepicker__triangle {
    display: none;
  }

  /* 📱 Медиа-запросы для адаптива */
  @media (max-width: 768px) {
    .react-datepicker {
      padding: 16px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 32px;
      height: 32px;
      font-size: 13px;
    }

    .react-datepicker__current-month {
      font-size: 16px;
    }

    .react-datepicker__navigation {
      top: 24px;
      width: 22px;
      height: 22px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 16px;
    }
  }

  @media (max-width: 480px) {
    .react-datepicker {
      padding: 12px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }

    .react-datepicker__current-month {
      font-size: 14px;
    }

    .react-datepicker__navigation {
      top: 20px;
      width: 20px;
      height: 20px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 12px;
    }
  }
`;
CalendarContainer.displayName = "CalendarContainer";

// Стили для уведомления об успешной отправке предложения
const SuccessNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #28a745;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  max-width: 90%;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
SuccessNotification.displayName = "SuccessNotification";

const SuccessNotificationText = styled.span`
  flex: 1;
`;
SuccessNotificationText.displayName = "SuccessNotificationText";

const SuccessNotificationClose = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;
SuccessNotificationClose.displayName = "SuccessNotificationClose";

const TenderProposalClient = ({ tenderId }) => {
  const router = useRouter();
  const { user } = useAuth();
  const [tenderInfo, setTenderInfo] = useState(null);
  const [tenderDetails, setTenderDetails] = useState(null); // Детальная информация о тендере
  const [tenderPhotos, setTenderPhotos] = useState([]); // Фотографии тендера
  const [proposalData, setProposalData] = useState([]);
  const [isDateModalOpen, setIsDateModalOpen] = useState(false);
  const [validUntilDate, setValidUntilDate] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [submittedProposals, setSubmittedProposals] = useState(new Set());

  // Функция для загрузки материалов тендера
  const fetchTenderMaterials = async (purchReqId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
      );

      if (!response.ok) {
        throw new Error(`Ошибка загрузки материалов: ${response.status}`);
      }

      const materials = await response.json();
      return Array.isArray(materials) ? materials : [];
    } catch (err) {
      console.error("Ошибка при загрузке материалов:", err);
      throw err;
    }
  };

  // Функция для загрузки детальной информации о тендере
  const fetchTenderDetails = async (purchReqId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTables/${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        console.warn(`Не удалось загрузить детали тендера ${purchReqId}`);
        return null;
      }
    } catch (error) {
      console.error("Ошибка при загрузке деталей тендера:", error);
      return null;
    }
  };

  // Функция для загрузки фотографий тендера
  const fetchTenderPhotos = async (purchReqId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTablePhotos?purchReqId=${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      } else {
        console.warn(`Не удалось загрузить фотографии тендера ${purchReqId}`);
        return [];
      }
    } catch (error) {
      console.error("Ошибка при загрузке фотографий тендера:", error);
      return [];
    }
  };

  useEffect(() => {
    const loadTenderData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Сначала пытаемся получить базовую информацию из localStorage
        let tenderBasicInfo = null;
        if (typeof window !== "undefined") {
          try {
            const saved = localStorage.getItem("selectedTenderInfo");
            const tender = saved ? JSON.parse(saved) : null;

            if (tender && tender.PurchReqId.toString() === tenderId) {
              tenderBasicInfo = tender;
            }
          } catch (error) {
            console.warn("Ошибка при чтении localStorage:", error);
          }
        }

        // Загружаем данные параллельно
        const [materials, tenderDetailsData, tenderPhotosData] =
          await Promise.all([
            fetchTenderMaterials(tenderId),
            fetchTenderDetails(tenderId),
            fetchTenderPhotos(tenderId),
          ]);

        // Сохраняем детальную информацию и фотографии
        setTenderDetails(tenderDetailsData);
        setTenderPhotos(tenderPhotosData);

        // Формируем полную информацию о тендере
        const fullTenderInfo = {
          PurchReqId: tenderId,
          PurchReqName:
            tenderDetailsData?.PurchReqName ||
            tenderBasicInfo?.PurchReqName ||
            `Тендер №${tenderId}`,
          PurchEndDate:
            tenderDetailsData?.PurchEndDate ||
            tenderBasicInfo?.PurchEndDate ||
            null,
          DeliveryAddress:
            tenderDetailsData?.DeliveryAddress ||
            tenderBasicInfo?.DeliveryAddress ||
            null,
          Description:
            tenderDetailsData?.Description ||
            tenderBasicInfo?.Description ||
            null,
          materials: materials,
        };

        setTenderInfo(fullTenderInfo);

        // Инициализируем форму для каждого материала
        setProposalData(
          materials.map((material) => ({
            materialId: material.MaterialId,
            materialName: material.MaterialName,
            purchUnit: material.PurchUnit,
            purchQty: material.PurchQty,
            purchOpenPrice: material.PurchOpenPrice,
            Description: material.Description,
            // Значения из API для отображения требований заказчика
            originalOnly: material.OriginalOnly, // true - соответствует запросу, false - можно аналог
            priceWithDelivery: material.PriceWithDelivery, // true - с доставкой, false - без
            // Значения для формы предложения (по умолчанию)
            retailPrice: "",
            priceType: "with-delivery", // "with-delivery" или "without-delivery"
            matchesRequest: true, // true - соответствует запросу, false - аналог
            comments: "",
            attachedFiles: [], // массив прикрепленных файлов
          }))
        );
      } catch (err) {
        setError(err.message);
        console.error("Ошибка при загрузке данных тендера:", err);
      } finally {
        setIsLoading(false);
      }
    };

    if (tenderId) {
      loadTenderData();
    }
  }, [tenderId]);

  const handleProposalDataChange = (index, field, value) => {
    setProposalData((prev) => {
      const newData = [...prev];
      newData[index] = { ...newData[index], [field]: value };
      return newData;
    });
  };

  const handleSubmit = () => {
    // Валидация
    const incompleteProposals = proposalData.filter(
      (proposal) => !proposal.retailPrice
    );

    if (incompleteProposals.length > 0) {
      alert("Укажите цены для всех материалов");
      return;
    }

    const submissionData = {
      tenderId: tenderInfo.PurchReqId,
      tenderName: tenderInfo.PurchReqName,
      validUntilDate: validUntilDate,
      proposals: proposalData.map((proposal) => ({
        ...proposal,
        attachedFilesCount: proposal.attachedFiles
          ? proposal.attachedFiles.length
          : 0,
        attachedFilesInfo: proposal.attachedFiles
          ? proposal.attachedFiles.map((f) => ({
              name: f.name,
              size: f.size,
              type: f.type,
            }))
          : [],
      })),
      submittedAt: new Date().toISOString(),
    };

    console.log("Подача предложения:", submissionData);

    // В реальном приложении здесь бы был FormData для отправки файлов
    const totalFiles = proposalData.reduce(
      (total, proposal) =>
        total + (proposal.attachedFiles ? proposal.attachedFiles.length : 0),
      0
    );

    if (totalFiles > 0) {
      console.log(`Прикреплено файлов: ${totalFiles}`);
    }
    alert("Предложение успешно отправлено!");

    // Очищаем localStorage и возвращаемся к поиску тендеров
    try {
      localStorage.removeItem("selectedTenderInfo");
    } catch (error) {
      console.error("Ошибка при очистке localStorage:", error);
    }

    router.push("/find-tender");
  };

  const handleSubmitSingleProposal = async (index) => {
    const proposal = proposalData[index];

    // Валидация для конкретного материала
    if (!proposal.retailPrice) {
      alert("Укажите цену для материала");
      return;
    }

    if (!validUntilDate) {
      alert("Выберите дату актуальности предложения");
      return;
    }

    // Проверяем авторизацию и данные компании
    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000"
    ) {
      alert("Ошибка авторизации. Пожалуйста, войдите в систему заново.");
      return;
    }

    if (
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      window.location.href = "/auth?from=company";
      return;
    }

    try {
      // Получаем заголовки авторизации
      const authHeaders = await authService.getAuthHeaders();

      // Находим соответствующий материал из tenderInfo для получения PurchReqLineId
      const material = tenderInfo.materials.find(
        (m) => m.MaterialId === proposal.materialId
      );
      if (!material) {
        alert("Ошибка: не найдена информация о материале");
        return;
      }

      const requestData = {
        PurchReqPriceId: 0,
        PurchReqId: tenderInfo.PurchReqId,
        PurchReqLineId: material.PurchReqLineId,
        MaterialId: proposal.materialId,
        ProviderId: user.companyId,
        UserId: user.userId,
        OfferQty: 1,
        OfferPrice: parseFloat(proposal.retailPrice),
        OfferSum: parseFloat(proposal.retailPrice),
        PurchReqPriceDate: new Date().toISOString(),
        ActualDate: validUntilDate.toISOString(),
        Description: proposal.comments || "",
        IsAccept: false,
        AcceptQty: 0,
        AcceptPrice: 0,
        IsOriginal: proposal.matchesRequest,
        PriceWithDelivery: proposal.priceType === "with-delivery",
      };

      console.log("📤 Отправка предложения:", requestData);
      console.log("🔑 Заголовки авторизации:", authHeaders);

      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqPrices`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...authHeaders,
          },
          body: JSON.stringify(requestData),
        }
      );

      if (response.ok) {
        // Добавляем материал в список отправленных
        setSubmittedProposals(
          (prev) => new Set([...prev, proposal.materialId])
        );

        // Показываем уведомление об успехе
        setShowSuccessNotification(true);

        // Автоматически скрываем уведомление через 5 секунд
        setTimeout(() => {
          setShowSuccessNotification(false);
        }, 5000);

        console.log("✅ Предложение успешно отправлено");
      } else {
        const errorData = await response.text();
        console.error(
          "❌ Ошибка при отправке предложения:",
          response.status,
          errorData
        );
        alert("Ошибка при отправке предложения. Попробуйте еще раз.");
      }
    } catch (error) {
      console.error("❌ Ошибка при отправке предложения:", error);
      alert("Произошла ошибка при отправке предложения. Попробуйте еще раз.");
    }
  };

  const handleBack = () => {
    router.push("/find-tender");
  };

  const handleOpenDateModal = () => {
    setIsDateModalOpen(true);
  };

  const handleCloseDateModal = () => {
    setIsDateModalOpen(false);
  };

  const handleDateSelect = (date) => {
    setValidUntilDate(date);
    setIsDateModalOpen(false);
  };

  // Функция для закрытия уведомления об успешной отправке
  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false);
  };

  // Функция для форматирования размера файла
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Функция для обработки выбора файлов
  const handleFileSelect = (index, event) => {
    const files = Array.from(event.target.files);

    if (files.length === 0) return;

    // Проверяем размер файлов (максимум 10MB на файл)
    const maxSize = 10 * 1024 * 1024; // 10MB
    const oversizedFiles = files.filter((file) => file.size > maxSize);

    if (oversizedFiles.length > 0) {
      alert(
        `Файлы слишком большие. Максимальный размер: 10MB\n${oversizedFiles
          .map((f) => f.name)
          .join("\n")}`
      );
      return;
    }

    // Добавляем файлы к существующим
    setProposalData((prev) => {
      const newData = [...prev];
      const currentFiles = newData[index].attachedFiles || [];

      // Создаем объекты файлов с дополнительной информацией
      const newFiles = files.map((file) => ({
        file: file,
        name: file.name,
        size: file.size,
        type: file.type,
        id: Date.now() + Math.random(), // уникальный ID
      }));

      newData[index] = {
        ...newData[index],
        attachedFiles: [...currentFiles, ...newFiles],
      };

      return newData;
    });

    // Очищаем input для возможности повторного выбора того же файла
    event.target.value = "";
  };

  // Функция для удаления файла
  const handleRemoveFile = (proposalIndex, fileId) => {
    setProposalData((prev) => {
      const newData = [...prev];
      newData[proposalIndex] = {
        ...newData[proposalIndex],
        attachedFiles: newData[proposalIndex].attachedFiles.filter(
          (file) => file.id !== fileId
        ),
      };
      return newData;
    });
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Не указано";
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear().toString().slice(-2);
    return `${day}.${month}.${year}`;
  };

  if (isLoading) {
    return (
      <>
        <BackButtonMiniSection>
          <BackButton onClick={handleBack} style={{ marginBottom: "20px" }}>
            <img
              src="/icons/arrow_back_24px.svg"
              alt="Назад"
              style={{ width: "12px", height: "12px" }}
            />{" "}
            ВЕРНУТЬСЯ К СПИСКУ ТЕНДЕРОВ
          </BackButton>
        </BackButtonMiniSection>
        <TenderProposalContainer>
          <ContentContainer>
            <Title>Загрузка тендера...</Title>
            <NoTenderMessage>
              Пожалуйста, подождите. Загружаем информацию о тендере и
              материалах.
            </NoTenderMessage>
          </ContentContainer>
        </TenderProposalContainer>
      </>
    );
  }

  if (error) {
    return (
      <TenderProposalContainer>
        <ContentContainer>
          <Title>Ошибка загрузки</Title>
          <NoTenderMessage>
            Не удалось загрузить информацию о тендере: {error}
          </NoTenderMessage>
        </ContentContainer>
      </TenderProposalContainer>
    );
  }

  if (
    !tenderInfo ||
    !tenderInfo.materials ||
    tenderInfo.materials.length === 0
  ) {
    return (
      <>
        <BackButtonMiniSection>
          <BackButton onClick={handleBack} style={{ marginBottom: "20px" }}>
            <img
              src="/icons/arrow_back_24px.svg"
              alt="Назад"
              style={{ width: "12px", height: "12px" }}
            />{" "}
            ВЕРНУТЬСЯ К СПИСКУ ТЕНДЕРОВ
          </BackButton>
        </BackButtonMiniSection>
        <TenderProposalContainer>
          <ContentContainer>
            <Title>Тендер не найден</Title>
            <NoTenderMessage>
              Информация о тендере недоступна или в тендере нет материалов.
            </NoTenderMessage>
          </ContentContainer>
        </TenderProposalContainer>
      </>
    );
  }

  return (
    <>
      {/* Уведомление об успешной отправке предложения */}
      {showSuccessNotification && (
        <SuccessNotification>
          <SuccessNotificationText>
            Предложение успешно отправлено!
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseSuccessNotification}>
            ×
          </SuccessNotificationClose>
        </SuccessNotification>
      )}

      <BackButtonMiniSection>
        <BackButton onClick={handleBack} style={{ marginBottom: "20px" }}>
          <img
            src="/icons/arrow_back_24px.svg"
            alt="Назад"
            style={{ width: "12px", height: "12px" }}
          />{" "}
          ВЕРНУТЬСЯ К СПИСКУ ТЕНДЕРОВ
        </BackButton>
      </BackButtonMiniSection>
      <TenderProposalContainer>
        <ContentContainer>
          <Title>Подача предложения</Title>

          {/* Информация о тендере */}
          {tenderDetails && (
            <ProductFormCard
              style={{ marginBottom: "24px", backgroundColor: "#f0f8ff" }}
            >
              <ProductInfo>
                <ProductTitle>{tenderDetails.PurchReqName}</ProductTitle>
                <Label>
                  Дата окончания:{" "}
                  {tenderDetails.PurchEndDate
                    ? new Date(tenderDetails.PurchEndDate).toLocaleDateString(
                        "ru-RU"
                      )
                    : "Не указано"}
                </Label>
                <Label>
                  Адрес доставки: {tenderDetails.DeliveryAddress || "Не указан"}
                </Label>
                {tenderDetails.Description && (
                  <Label>Описание: {tenderDetails.Description}</Label>
                )}
              </ProductInfo>
            </ProductFormCard>
          )}

          {/* Фотографии тендера */}
          {tenderPhotos && tenderPhotos.length > 0 && (
            <ProductFormCard style={{ marginBottom: "24px" }}>
              <ProductInfo>
                <ProductTitle>Прикрепленные файлы</ProductTitle>
                <div
                  style={{
                    display: "flex",
                    flexWrap: "wrap",
                    gap: "12px",
                    marginTop: "12px",
                  }}
                >
                  {tenderPhotos.map((photo, index) => (
                    <div
                      key={photo.PurchReqTableFotoId}
                      style={{ position: "relative" }}
                    >
                      <img
                        src={photo.FileUrl}
                        alt={`Фото тендера ${index + 1}`}
                        style={{
                          width: "150px",
                          height: "150px",
                          objectFit: "cover",
                          borderRadius: "4px",
                          border: "1px solid #ddd",
                        }}
                        onError={(e) => {
                          e.target.style.display = "none";
                        }}
                      />
                      <div
                        style={{
                          position: "absolute",
                          bottom: "4px",
                          left: "4px",
                          right: "4px",
                          background: "rgba(0,0,0,0.7)",
                          color: "white",
                          fontSize: "10px",
                          padding: "2px 4px",
                          borderRadius: "2px",
                          textAlign: "center",
                        }}
                      >
                        {photo.FileName}
                      </div>
                    </div>
                  ))}
                </div>
              </ProductInfo>
            </ProductFormCard>
          )}

          <SectionTitle>Список закупок</SectionTitle>
          <Text>
            Укажите важные детали продукции, вашу цену, сроки и условия
            поставок.
          </Text>

          {proposalData.map((proposal, index) => (
            <ProductFormCard key={proposal.materialId}>
              <ProductInfo>
                <ProductId>{proposal.materialId}</ProductId>
                <Label>Единица измерения: {proposal.purchUnit}</Label>
                <ProductTitle>{proposal.materialName}</ProductTitle>
                <Label style={{ marginBottom: 16 }}>
                  Количество: {proposal.purchQty} {proposal.purchUnit}
                </Label>
                <Label style={{ marginBottom: 16 }}>
                  {(proposal.purchOpenPrice ?? "Цена не указана") +
                    (proposal.purchOpenPrice
                      ? ` ₸ за ${proposal.purchUnit}`
                      : "")}
                </Label>
                <ActionContainer>
                  <ActionButtonContainer>
                    <ActionButton>Цена с доставкой</ActionButton>
                    <ActionButton>Без</ActionButton>
                  </ActionButtonContainer>
                  <ActionButtonContainer>
                    <ActionButton>Соответсвует запросу</ActionButton>
                    <ActionButton>Можно аналог</ActionButton>
                  </ActionButtonContainer>
                </ActionContainer>
                <Label style={{ marginBottom: 16 }}>
                  Комментарии заказчика: {proposal.Description}
                </Label>
                {/* <Label style={{ marginBottom: 16 }}>
                  Дата актуальности предложения: до {proposal.PurchEndDate}
                </Label> */}
              </ProductInfo>

              <FormRow>
                <SmallFormGroup>
                  <Input
                    type="number"
                    value={proposal.retailPrice}
                    onChange={(e) =>
                      handleProposalDataChange(
                        index,
                        "retailPrice",
                        e.target.value
                      )
                    }
                    placeholder="Ваша цена"
                    required
                  />
                  <span
                    style={{
                      position: "absolute",
                      right: "8px",
                      bottom: "8px",
                      color: "#656D78",
                      fontSize: "14px",
                    }}
                  >
                    ₸ за {proposal.purchUnit}
                  </span>
                </SmallFormGroup>

                <ActionButtonContainer>
                  <ActionButton
                    active={proposal.priceType === "with-delivery"}
                    onClick={() =>
                      handleProposalDataChange(
                        index,
                        "priceType",
                        "with-delivery"
                      )
                    }
                  >
                    Цена с доставкой
                  </ActionButton>
                  <ActionButton
                    active={proposal.priceType === "without-delivery"}
                    onClick={() =>
                      handleProposalDataChange(
                        index,
                        "priceType",
                        "without-delivery"
                      )
                    }
                  >
                    Без
                  </ActionButton>
                </ActionButtonContainer>

                <ActionButtonContainer>
                  <ActionButton
                    active={proposal.matchesRequest === true}
                    onClick={() =>
                      handleProposalDataChange(index, "matchesRequest", true)
                    }
                  >
                    Соответствует запросу
                  </ActionButton>
                  <ActionButton
                    active={proposal.matchesRequest === false}
                    onClick={() =>
                      handleProposalDataChange(index, "matchesRequest", false)
                    }
                  >
                    Аналог
                  </ActionButton>
                </ActionButtonContainer>
              </FormRow>

              <Label style={{ fontSize: "17px", color: "#656D78" }}>
                Комментарий к предложению
              </Label>

              <TextArea
                value={proposal.comments}
                onChange={(e) =>
                  handleProposalDataChange(index, "comments", e.target.value)
                }
                placeholder="Например: Предлагаемый нами продукт соответствует всем стандартам. "
              />

              <DateButton onClick={handleOpenDateModal}>
                {validUntilDate
                  ? `Ценовое предложение актуально до: ${formatDate(
                      validUntilDate
                    )}`
                  : "Ценовое предложение актуально до:"}

                <img src="/icons/Cell/Vector.svg" alt="Календарь" />
              </DateButton>
              <UploadButton
                onClick={() =>
                  document.getElementById(`file-input-${index}`).click()
                }
              >
                <img src="/icons/Upload.svg" alt="Загрузить" />
                <UploadText>Прикрепить файл</UploadText>
              </UploadButton>

              <HiddenFileInput
                id={`file-input-${index}`}
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.zip,.rar"
                onChange={(e) => handleFileSelect(index, e)}
              />

              {/* Список прикрепленных файлов */}
              {proposal.attachedFiles && proposal.attachedFiles.length > 0 && (
                <AttachedFilesList>
                  {proposal.attachedFiles.map((fileObj) => (
                    <AttachedFileItem key={fileObj.id}>
                      <FileInfo>
                        <FileName>{fileObj.name}</FileName>
                        <FileSize>{formatFileSize(fileObj.size)}</FileSize>
                      </FileInfo>
                      <RemoveFileButton
                        onClick={() => handleRemoveFile(index, fileObj.id)}
                        title="Удалить файл"
                      >
                        ×
                      </RemoveFileButton>
                    </AttachedFileItem>
                  ))}
                </AttachedFilesList>
              )}
              <Label>
                Фотографии, сертификаты качества, другая полезная информация
              </Label>

              {/* Кнопка отправки предложения для каждого материала */}
              <SubmitButton
                onClick={() => handleSubmitSingleProposal(index)}
                disabled={submittedProposals.has(proposal.materialId)}
                style={{
                  margin: "16px 0 0 0",
                  width: "100%",
                  backgroundColor: submittedProposals.has(proposal.materialId)
                    ? "#6c757d"
                    : "#0066cc",
                  cursor: submittedProposals.has(proposal.materialId)
                    ? "not-allowed"
                    : "pointer",
                }}
              >
                {submittedProposals.has(proposal.materialId)
                  ? "Предложение отправлено"
                  : "Отправить предложение"}
              </SubmitButton>
            </ProductFormCard>
          ))}
        </ContentContainer>
      </TenderProposalContainer>

      {/* Модальное окно с календарем */}
      {isDateModalOpen && (
        <ModalOverlay onClick={handleCloseDateModal}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>Выберите дату актуальности предложения</ModalTitle>
              <CloseButton onClick={handleCloseDateModal}>×</CloseButton>
            </ModalHeader>

            <CalendarContainer>
              <DatePicker
                selected={validUntilDate}
                onChange={handleDateSelect}
                dateFormat="dd MMMM yyyy"
                locale="ru"
                calendarStartDay={1}
                inline
                minDate={new Date()} // Не позволяем выбирать прошедшие даты
              />
            </CalendarContainer>
          </ModalContent>
        </ModalOverlay>
      )}
    </>
  );
};

export default TenderProposalClient;
