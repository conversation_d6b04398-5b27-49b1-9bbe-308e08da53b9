"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import API_CONFIG from "../../config/api";
import { useAuth } from "../../context/AuthContext";

const MyTendersContainer = styled.div``;
MyTendersContainer.displayName = "MyTendersContainer";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  text-align: center;
  margin-top: 40px;
  color: #434a54;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const ContentSection = styled.div`
  background-color: white;
  flex-grow: 1;
  padding: 10px 20px;
  min-height: 46vh;
  margin-top: 20px;

  @media (max-width: 768px) {
    padding: 24px 16px;
  }
`;
ContentSection.displayName = "ContentSection";

const TenderResultsContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
TenderResultsContainer.displayName = "TenderResultsContainer";

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
`;
LoadingMessage.displayName = "LoadingMessage";

const ErrorMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  margin: 20px 0;
`;
ErrorMessage.displayName = "ErrorMessage";

const NoTendersMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  font-size: 18px;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
`;
NoTendersMessage.displayName = "NoTendersMessage";

const TenderResultsGrid = styled.div`
  display: grid;
  gap: 20px;
  margin-top: 20px;

  @media (max-width: 768px) {
    gap: 16px;
  }
`;
TenderResultsGrid.displayName = "TenderResultsGrid";

const TenderCard = styled.div`
  background-color: #e8e8e8;
  border-radius: 4px;
  padding: 20px;
  border: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  animation: fadeInUp 0.3s ease forwards;
  opacity: 0;
  transform: translateY(20px);

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;
TenderCard.displayName = "TenderCard";

const ViewTenderButton = styled.button`
  background-color: transparent;
  border: 2px solid #0066cc;
  color: #0066cc;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f0f8ff;
  }
`;
ViewTenderButton.displayName = "ViewTenderButton";

const TenderTitle = styled.h3`
  font-size: 24px;
  font-weight: 900;
  color: #434a54;
  margin: 0 0 8px 0;
`;
TenderTitle.displayName = "TenderTitle";

const TenderSubtitle = styled.div`
  font-size: 14px;
  color: #434a54;
  margin-bottom: 4px;
  font-weight: 700;
  text-transform: uppercase;
`;
TenderSubtitle.displayName = "TenderSubtitle";

const TenderDeadline = styled.div`
  font-size: 14px;
  font-weight: 700;
  color: #434a54;
  margin-bottom: 16px;
  text-transform: uppercase;
`;
TenderDeadline.displayName = "TenderDeadline";

// Стили для материалов тендера (точно как в FindTenderClient)
const MaterialsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;
MaterialsList.displayName = "MaterialsList";

const MaterialItem = styled.div`
  font-size: 14px;
  line-height: 1.4;
  padding-bottom: 10px; /* смещает border вниз */
  border-bottom: 1px solid #919399;
`;
MaterialItem.displayName = "MaterialItem";

const MaterialId = styled.div`
  color: #969ea7;
  font-size: 14px;
  font-weight: 400;
  text-transform: uppercase;
  margin-bottom: 2px;
`;
MaterialId.displayName = "MaterialId";

const MaterialUnit = styled.div`
  color: #969ea7;
  font-size: 14px;
  margin-bottom: 4px;
`;
MaterialUnit.displayName = "MaterialUnit";

const MaterialName = styled.div`
  color: #434a54;
  font-size: 17px;
  font-weight: 400;
`;
MaterialName.displayName = "MaterialName";

const MaterialQty = styled.div`
  color: #434a54;
  font-size: 17px;
  font-weight: 400;
`;
MaterialQty.displayName = "MaterialQty";

const MaterialOpenPrice = styled.div`
  color: #434a54;
  font-size: 17px;
  font-weight: 400;
`;
MaterialOpenPrice.displayName = "MaterialOpenPrice";

const MaterialsLoading = styled.div`
  color: #666;
  font-size: 14px;
  font-style: italic;
  padding: 8px 0;
`;
MaterialsLoading.displayName = "MaterialsLoading";

const NoMaterials = styled.div`
  color: #666;
  font-size: 14px;
  font-style: italic;
  padding: 8px 0;
`;
NoMaterials.displayName = "NoMaterials";

// Стили для пагинации
const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 32px;
  padding: 20px 0;
`;
PaginationContainer.displayName = "PaginationContainer";

const PaginationButton = styled.button`
  width: 40px;
  height: 40px;
  border: 1px solid #e0e0e0;
  background-color: ${(props) => (props.active ? "#1976d2" : "white")};
  color: ${(props) => (props.active ? "white" : "#333")};
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background-color: ${(props) => (props.active ? "#1565c0" : "#f5f5f5")};
    /* border-color: #1976d2; */
  }

  &:disabled {
    background-color: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
    border-color: #e0e0e0;
  }
`;
PaginationButton.displayName = "PaginationButton";

const PaginationDots = styled.span`
  color: #666;
  font-size: 14px;
  padding: 0 4px;
`;
PaginationDots.displayName = "PaginationDots";

const MyTendersClient = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [tenders, setTenders] = useState([]);
  const [tenderMaterials, setTenderMaterials] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [isMaterialsLoading, setIsMaterialsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Состояние для пагинации
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 2; // 2 карточки на страницу

  // Проверка авторизации при загрузке компонента
  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth?from=tender");
      return;
    }

    // Проверяем companyId
    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000" ||
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      router.push("/auth?from=company");
      return;
    }

    fetchMyTenders();
  }, [isAuthenticated, user, router]);

  // Логика пагинации
  const totalPages = Math.ceil(tenders.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTenders = tenders.slice(startIndex, endIndex);

  // Загрузка материалов для текущих тендеров при смене страницы
  useEffect(() => {
    if (currentTenders.length > 0) {
      const purchReqIds = currentTenders.map((tender) => tender.PurchReqId);
      fetchTenderMaterials(purchReqIds);
    }
  }, [currentPage, tenders]);

  const fetchMyTenders = async () => {
    if (!user?.userId) return;

    try {
      setIsLoading(true);
      setError(null);

      const url = `${API_CONFIG.BASE_URL}/api/PurchReqTables/GetCusMyTenders?UserId=${user.userId}&purchStatus=1&isSpecification=false`;
      console.log("Запрос моих тендеров:", url);

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Получены мои тендеры:", data);
        setTenders(data || []);

        // Сбрасываем на первую страницу при новой загрузке
        setCurrentPage(1);
      } else {
        throw new Error(`Ошибка загрузки тендеров: ${response.status}`);
      }
    } catch (error) {
      console.error("Ошибка при загрузке моих тендеров:", error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTenderMaterials = async (purchReqIds) => {
    setIsMaterialsLoading(true);

    try {
      // Создаем массив промисов для всех запросов
      const promises = purchReqIds.map(async (purchReqId) => {
        const response = await fetch(
          `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
        );

        if (!response.ok) {
          console.warn(
            `Ошибка загрузки материалов для тендера ${purchReqId}: ${response.status}`
          );
          return { purchReqId, materials: [] };
        }

        const materials = await response.json();
        return { purchReqId, materials: materials || [] };
      });

      // Ждем выполнения всех запросов
      const results = await Promise.all(promises);

      // Преобразуем результаты в объект
      const materialsData = {};
      results.forEach(({ purchReqId, materials }) => {
        materialsData[purchReqId] = materials;
      });

      setTenderMaterials((prev) => ({ ...prev, ...materialsData }));
    } catch (error) {
      console.error("Ошибка при загрузке материалов тендеров:", error);
    } finally {
      setIsMaterialsLoading(false);
    }
  };

  // Функция форматирования даты
  const formatDate = (dateString) => {
    if (!dateString) return "Не указано";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("ru-RU", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      });
    } catch (error) {
      return "Неверная дата";
    }
  };

  // Функция для перехода к просмотру ценовых предложений
  const handleViewProposals = (purchReqId) => {
    // Проверяем авторизацию
    if (!isAuthenticated) {
      router.push("/auth?from=tender");
      return;
    }

    // Сохраняем базовую информацию о тендере в localStorage (как fallback)
    const tenderData = tenders.find(
      (tender) => tender.PurchReqId === purchReqId
    );

    if (tenderData) {
      try {
        // Сохраняем только основную информацию, материалы будут загружены через API
        localStorage.setItem("selectedTenderInfo", JSON.stringify(tenderData));
        // Переходим на страницу просмотра ценовых предложений
        router.push(`/tender-proposals/${purchReqId}`);
      } catch (error) {
        console.error("Ошибка при сохранении данных тендера:", error);
        // Если localStorage не работает, все равно переходим на страницу
        router.push(`/tender-proposals/${purchReqId}`);
      }
    } else {
      // Если тендер не найден в локальных данных, все равно переходим
      router.push(`/tender-proposals/${purchReqId}`);
    }
  };

  // Функции для пагинации
  const handlePageChange = (page) => {
    setCurrentPage(page);
    // Прокручиваем к началу результатов
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  // Функция для генерации номеров страниц для отображения
  const getPageNumbers = () => {
    const pages = [];

    if (totalPages <= 5) {
      // Если страниц мало, показываем все
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Если страниц много, показываем с многоточием
      if (currentPage <= 3) {
        // Начало: 1, 2, 3, ..., last
        pages.push(1, 2, 3, "...", totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Конец: 1, ..., last-2, last-1, last
        pages.push(1, "...", totalPages - 2, totalPages - 1, totalPages);
      } else {
        // Середина: 1, ..., current-1, current, current+1, ..., last
        pages.push(
          1,
          "...",
          currentPage - 1,
          currentPage,
          currentPage + 1,
          "...",
          totalPages
        );
      }
    }

    return pages;
  };

  if (isLoading) {
    return (
      <MyTendersContainer>
        <Title>Мои тендеры</Title>
        <ContentSection>
          <LoadingMessage>Загрузка ваших тендеров...</LoadingMessage>
        </ContentSection>
      </MyTendersContainer>
    );
  }

  if (error) {
    return (
      <MyTendersContainer>
        <Title>Мои тендеры</Title>
        <ContentSection>
          <ErrorMessage>Ошибка загрузки тендеров: {error}</ErrorMessage>
        </ContentSection>
      </MyTendersContainer>
    );
  }

  return (
    <MyTendersContainer>
      <Title>Мои тендеры</Title>

      <ContentSection>
        <TenderResultsContainer>
          {tenders.length === 0 ? (
            <NoTendersMessage>
              У вас пока нет созданных тендеров.
              <br />
              <br />
              <a
                href="/create-tender"
                style={{
                  color: "#007bff",
                  textDecoration: "none",
                }}
              >
                Создать первый тендер
              </a>
            </NoTendersMessage>
          ) : (
            <TenderResultsGrid>
              {currentTenders.map((tender, index) => {
                const materialsCount = tenderMaterials[tender.PurchReqId]
                  ? tenderMaterials[tender.PurchReqId].length
                  : 0;

                return (
                  <TenderCard
                    key={tender.PurchReqId}
                    style={{
                      animationDelay: `${index * 0.05}s`,
                    }}
                  >
                    <TenderTitle>{tender.PurchReqName}</TenderTitle>
                    <TenderSubtitle>
                      {materialsCount > 0
                        ? `${materialsCount} ${
                            materialsCount === 1
                              ? "позиция"
                              : materialsCount < 5
                              ? "позиции"
                              : "позиций"
                          } в тендере`
                        : "Загрузка позиций..."}
                    </TenderSubtitle>
                    <TenderDeadline>
                      Поставить до: {formatDate(tender.PurchEndDate)}
                    </TenderDeadline>

                    {/* Материалы тендера */}
                    {isMaterialsLoading ? (
                      <MaterialsLoading>
                        Загрузка материалов...
                      </MaterialsLoading>
                    ) : tenderMaterials[tender.PurchReqId] &&
                      tenderMaterials[tender.PurchReqId].length > 0 ? (
                      <MaterialsList>
                        {tenderMaterials[tender.PurchReqId]
                          .slice(0, 3)
                          .map((material) => (
                            <MaterialItem key={material.PurchReqLineId}>
                              <MaterialId>{material.MaterialId}</MaterialId>
                              <MaterialUnit>
                                Единица измерения: {material.PurchUnit}
                              </MaterialUnit>
                              <MaterialName>
                                {material.MaterialName}
                              </MaterialName>
                              <MaterialQty>
                                {material.PurchQty} {material.PurchUnit}
                              </MaterialQty>
                              <MaterialOpenPrice>
                                {(material.PurchOpenPrice ??
                                  "Цена не указана") +
                                  (material.PurchOpenPrice
                                    ? ` ₸ за ${material.PurchUnit}`
                                    : "")}
                              </MaterialOpenPrice>
                            </MaterialItem>
                          ))}
                      </MaterialsList>
                    ) : (
                      <NoMaterials>Материалы не найдены</NoMaterials>
                    )}
                    <ViewTenderButton
                      onClick={() => handleViewProposals(tender.PurchReqId)}
                    >
                      Смотреть ценовые предложения
                      <img src="/icons/findtender.svg" />
                    </ViewTenderButton>
                    <ViewTenderButton>
                      Редактировать тендер
                      <img src="/icons/findtender.svg" />
                    </ViewTenderButton>
                  </TenderCard>
                );
              })}
            </TenderResultsGrid>
          )}

          {/* Пагинация */}
          {!isLoading && !error && tenders.length > itemsPerPage && (
            <PaginationContainer>
              {/* Кнопка "Назад" */}
              <PaginationButton
                onClick={handlePrevPage}
                disabled={currentPage === 1}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 12L6 8L10 4"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </PaginationButton>

              {/* Номера страниц */}
              {getPageNumbers().map((page, index) => (
                <React.Fragment key={index}>
                  {page === "..." ? (
                    <PaginationDots>...</PaginationDots>
                  ) : (
                    <PaginationButton
                      active={page === currentPage}
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </PaginationButton>
                  )}
                </React.Fragment>
              ))}

              {/* Кнопка "Вперед" */}
              <PaginationButton
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 4L10 8L6 12"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </PaginationButton>
            </PaginationContainer>
          )}
        </TenderResultsContainer>
      </ContentSection>
    </MyTendersContainer>
  );
};

export default MyTendersClient;
