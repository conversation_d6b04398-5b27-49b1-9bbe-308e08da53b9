"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import API_CONFIG from "../../../config/api";
import { useAuth } from "../../../context/AuthContext";

const TenderProposalsContainer = styled.div`
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderProposalsContainer.displayName = "TenderProposalsContainer";

const BackButtonMiniSection = styled.div`
  height: 70px;
  padding: 24px 160px 12px 160px;
`;
BackButtonMiniSection.displayName = "BackButtonMiniSection";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const BackButton = styled.button`
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;
  border: none;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 900;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const ProductFormCard = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;
ProductFormCard.displayName = "ProductFormCard";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 24px;
  font-weight: 400;
  color: #434a54;
  line-height: 32px;
  margin-bottom: 10px;
`;
ProductTitle.displayName = "ProductTitle";

const Label = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #969ea7;
  margin-bottom: 10px;
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  padding-right: 40px;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
Input.displayName = "Input";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 200px;
  position: relative;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 75px;
  width: 100%;
  resize: vertical;
  color: #434a54;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TextArea.displayName = "TextArea";

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

const DateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: white;
  border: 2px solid #0066cc;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  color: #0066cc;
  font-weight: 600;
  margin-top: 0px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f8ff;
  }
`;
DateButton.displayName = "DateButton";

const NoTenderMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;
NoTenderMessage.displayName = "NoTenderMessage";

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
`;
LoadingMessage.displayName = "LoadingMessage";

const ErrorMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 16px;
  color: #dc3545;
`;
ErrorMessage.displayName = "ErrorMessage";

const MyTenderProposalsClient = ({ tenderId }) => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [tenderInfo, setTenderInfo] = useState(null);
  const [materials, setMaterials] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const purchReqId = tenderId;

  // Функция для получения информации о тендере
  const fetchTenderInfo = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        setTenderInfo(data);
      } else {
        throw new Error("Не удалось загрузить информацию о тендере");
      }
    } catch (error) {
      console.error("Ошибка при загрузке тендера:", error);
      setError(error.message);
    }
  };

  // Функция для получения материалов тендера
  const fetchTenderMaterials = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        setMaterials(Array.isArray(data) ? data : []);
      } else {
        throw new Error("Не удалось загрузить материалы тендера");
      }
    } catch (error) {
      console.error("Ошибка при загрузке материалов:", error);
      setError(error.message);
    }
  };

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth");
      return;
    }

    const loadData = async () => {
      if (!purchReqId) {
        setError("ID тендера не указан");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Сначала пытаемся получить базовую информацию из localStorage
        let tenderBasicInfo = null;
        if (typeof window !== "undefined") {
          try {
            const saved = localStorage.getItem("selectedTenderInfo");
            const tender = saved ? JSON.parse(saved) : null;

            if (tender && tender.PurchReqId.toString() === purchReqId) {
              tenderBasicInfo = tender;
            }
          } catch (error) {
            console.warn("Ошибка при чтении localStorage:", error);
          }
        }

        // Загружаем все данные параллельно
        await Promise.all([fetchTenderInfo(), fetchTenderMaterials()]);
      } catch (err) {
        setError(err.message);
        console.error("Ошибка при загрузке данных:", err);
      } finally {
        setIsLoading(false);
      }
    };

    if (purchReqId) {
      loadData();
    }
  }, [purchReqId, isAuthenticated, router]);

  const handleBack = () => {
    router.push("/my-tenders");
  };

  if (isLoading) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <LoadingMessage>Загрузка ценовых предложений...</LoadingMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  if (error) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <ErrorMessage>Ошибка: {error}</ErrorMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  if (!tenderInfo) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <NoTenderMessage>Тендер не найден</NoTenderMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  return (
    <TenderProposalsContainer>
      <BackButtonMiniSection>
        <BackButton onClick={handleBack}>
          <img
            src="/icons/arrow_back_24px.svg"
            alt="Назад"
            style={{ width: "12px", height: "12px" }}
          />{" "}
          Назад к моим тендерам
        </BackButton>
      </BackButtonMiniSection>

      <ContentContainer>
        <Title>Ценовые предложения по тендеру</Title>

        <SectionTitle>Материалы тендера</SectionTitle>
        {materials.map((material) => (
          <ProductFormCard key={material.MaterialId}>
            <ProductInfo>
              <ProductId>{material.MaterialId}</ProductId>
              <Label>Единица измерения: {material.PurchUnit}</Label>
              <ProductTitle>{material.MaterialName}</ProductTitle>
              <Label style={{ marginBottom: 16 }}>
                {material.PurchQty} {material.PurchUnit}
              </Label>
              <Label style={{ marginBottom: 16 }}>
                Начальная цена:&nbsp;
                {(material.PurchOpenPrice ?? " Цена не указана") +
                  (material.PurchOpenPrice
                    ? ` ₸ за ${material.PurchUnit}`
                    : "")}
              </Label>
            </ProductInfo>

            <FormRow>
              <SmallFormGroup>
                <Input
                  type="text"
                  value="Просмотр"
                  placeholder="Ваша цена"
                  disabled
                  style={{ backgroundColor: "#f8f9fa", color: "#666" }}
                />
                <span
                  style={{
                    position: "absolute",
                    right: "8px",
                    bottom: "8px",
                    color: "#656D78",
                    fontSize: "14px",
                  }}
                >
                  ₸ за {material.PurchUnit}
                </span>
              </SmallFormGroup>

              <ActionButtonContainer>
                <ActionButton active={true}>Цена с доставкой</ActionButton>
                <ActionButton active={false}>Без</ActionButton>
              </ActionButtonContainer>

              <ActionButtonContainer>
                <ActionButton active={true}>Соответствует запросу</ActionButton>
                <ActionButton active={false}>Аналог</ActionButton>
              </ActionButtonContainer>
            </FormRow>

            <Label style={{ fontSize: "17px", color: "#656D78" }}>
              Комментарий к предложению
            </Label>

            <TextArea
              value="Например: Предлагаемый нами продукт соответствует всем стандартам."
              placeholder="Например: Предлагаемый нами продукт соответствует всем стандартам."
              disabled
              style={{ backgroundColor: "#f8f9fa", color: "#666" }}
            />

            <DateButton
              disabled
              style={{
                backgroundColor: "#f8f9fa",
                color: "#666",
                borderColor: "#ddd",
              }}
            >
              Ценовое предложение актуально до:
              <img src="/icons/Cell/Vector.svg" alt="Календарь" />
            </DateButton>
          </ProductFormCard>
        ))}
      </ContentContainer>
    </TenderProposalsContainer>
  );
};

export default MyTenderProposalsClient;
